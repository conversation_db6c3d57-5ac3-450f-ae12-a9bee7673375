import { useState, useEffect } from "react";
import {
  signIn,
  signOut,
  signUp,
  getCurrentUser,
  getCurrentSession,
  onAuthStateChange,
} from "../utils/signIn";

interface User {
  id: string;
  email?: string;
  user_metadata?: any;
  app_metadata?: any;
}

interface AuthState {
  user: User | null;
  session: any;
  loading: boolean;
  error: string | null;
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true,
    error: null,
  });

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        console.log("Getting initial session...");
        const session = await getCurrentSession();
        const user = await getCurrentUser();
        console.log("Initial session loaded:", {
          user: !!user,
          session: !!session,
        });
        setAuthState({
          user,
          session,
          loading: false,
          error: null,
        });
      } catch (error) {
        console.error("Error getting initial session:", error);
        setAuthState({
          user: null,
          session: null,
          loading: false,
          error:
            error instanceof Error ? error.message : "Authentication error",
        });
      }
    };

    getInitialSession();

    // Listen for auth changes
    try {
      const {
        data: { subscription },
      } = onAuthStateChange((event, session) => {
        console.log("Auth state changed:", event, !!session);
        setAuthState((prev) => ({
          ...prev,
          user: session?.user || null,
          session,
          loading: false,
          error: null,
        }));
      });

      return () => subscription.unsubscribe();
    } catch (error) {
      console.error("Error setting up auth listener:", error);
      return () => {}; // Return empty cleanup function
    }
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setAuthState((prev) => ({ ...prev, loading: true, error: null }));
      const user = await signIn(email, password);
      const session = await getCurrentSession();

      setAuthState({
        user,
        session,
        loading: false,
        error: null,
      });

      return user;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Login failed";
      setAuthState((prev) => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      throw error;
    }
  };

  const logout = async () => {
    try {
      setAuthState((prev) => ({ ...prev, loading: true, error: null }));
      await signOut();

      setAuthState({
        user: null,
        session: null,
        loading: false,
        error: null,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Logout failed";
      setAuthState((prev) => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      throw error;
    }
  };

  const register = async (email: string, password: string, metadata?: any) => {
    try {
      setAuthState((prev) => ({ ...prev, loading: true, error: null }));
      const user = await signUp(email, password, metadata);

      setAuthState((prev) => ({
        ...prev,
        user,
        loading: false,
        error: null,
      }));

      return user;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Registration failed";
      setAuthState((prev) => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      throw error;
    }
  };

  const clearError = () => {
    setAuthState((prev) => ({ ...prev, error: null }));
  };

  return {
    user: authState.user,
    session: authState.session,
    loading: authState.loading,
    error: authState.error,
    isAuthenticated: !!authState.user,
    login,
    logout,
    register,
    clearError,
  };
};
