import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  BookO<PERSON>, 
  Clock, 
  Users, 
  Target, 
  Zap, 
  Shield,
  Download,
  Search,
  Palette,
  BarChart3
} from "lucide-react";
import { useScrollReveal } from "@/hooks/useScrollReveal";
import { CookieBanner } from "@/components/CookieBanner";

export default function Features() {
  const { ref: heroRef, isVisible: heroVisible } = useScrollReveal();
  const { ref: feature1Ref, isVisible: feature1Visible } = useScrollReveal();
  const { ref: feature2Ref, isVisible: feature2Visible } = useScrollReveal();
  const { ref: feature3Ref, isVisible: feature3Visible } = useScrollReveal();
  const { ref: feature4Ref, isVisible: feature4Visible } = useScrollReveal();
  const { ref: feature5Ref, isVisible: feature5Visible } = useScrollReveal();
  const { ref: ctaRef, isVisible: ctaVisible } = useScrollReveal();

  const features = [
    {
      id: 1,
      title: "Curriculum-Aligned Lesson Plans",
      description: "Every lesson plan is meticulously crafted to align with national and state curriculum standards. Save hours of planning time while ensuring your students receive comprehensive, standards-based education that meets all learning objectives.",
      image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
      imageAlt: "Teacher planning lessons with curriculum materials",
      icon: Target,
      benefits: [
        "Standards-aligned content",
        "Grade-specific objectives",
        "Assessment rubrics included",
        "Cross-curricular connections"
      ],
      imageLeft: true,
      ref: feature1Ref,
      isVisible: feature1Visible
    },
    {
      id: 2,
      title: "Interactive Learning Activities",
      description: "Engage your students with hands-on activities, group projects, and interactive exercises designed to make learning memorable. Our lesson plans include diverse teaching methods that cater to different learning styles and keep students actively involved.",
      image: "https://images.pexels.com/photos/8617981/pexels-photo-8617981.jpeg",
      imageAlt: "Students engaged in interactive classroom activities",
      icon: Users,
      benefits: [
        "Hands-on experiments",
        "Group collaboration projects",
        "Digital interactive tools",
        "Multi-sensory learning approaches"
      ],
      imageLeft: false,
      ref: feature2Ref,
      isVisible: feature2Visible
    },
    {
      id: 3,
      title: "Time-Saving Templates",
      description: "Streamline your preparation with our ready-to-use templates and resources. From lesson plan outlines to assessment tools, everything is designed to reduce your workload while maintaining high-quality educational standards.",
      image: "https://images.unsplash.com/photo-1509062522246-3755977927d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
      imageAlt: "Organized teaching materials and planning templates",
      icon: Clock,
      benefits: [
        "Pre-designed lesson templates",
        "Printable worksheets",
        "Assessment tools",
        "Progress tracking sheets"
      ],
      imageLeft: true,
      ref: feature3Ref,
      isVisible: feature3Visible
    },
    {
      id: 4,
      title: "Customizable Content",
      description: "Adapt every lesson to fit your unique classroom needs. Our flexible content allows you to modify activities, adjust difficulty levels, and personalize learning experiences to match your students' abilities and interests.",
      image: "https://images.pexels.com/photos/8617961/pexels-photo-8617961.jpeg",
      imageAlt: "Teacher customizing lesson materials on computer",
      icon: Palette,
      benefits: [
        "Editable lesson components",
        "Difficulty level adjustments",
        "Personalization options",
        "Multiple format exports"
      ],
      imageLeft: false,
      ref: feature4Ref,
      isVisible: feature4Visible
    },
    {
      id: 5,
      title: "Progress Tracking & Analytics",
      description: "Monitor student progress with built-in assessment tools and analytics. Track learning outcomes, identify areas for improvement, and generate detailed reports to share with parents and administrators.",
      image: "https://images.pexels.com/photos/8199252/pexels-photo-8199252.jpeg",
      imageAlt: "Teacher reviewing student progress analytics on tablet",
      icon: BarChart3,
      benefits: [
        "Real-time progress tracking",
        "Detailed analytics reports",
        "Parent communication tools",
        "Performance insights"
      ],
      imageLeft: true,
      ref: feature5Ref,
      isVisible: feature5Visible
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <a href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
              <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg flex items-center justify-center">
                <BookOpen className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">lectur.io</span>
            </a>
            <div className="hidden md:flex items-center space-x-8">
              <a href="/" className="text-gray-600 hover:text-gray-900 transition-colors">
                Home
              </a>
              <a href="/features" className="text-blue-600 font-medium">
                Features
              </a>
              <a href="/plans" className="text-gray-600 hover:text-gray-900 transition-colors">
                Lesson Plans
              </a>
              <a href="/pricing" className="text-gray-600 hover:text-gray-900 transition-colors">
                Pricing
              </a>
              <a href="/blog" className="text-gray-600 hover:text-gray-900 transition-colors">
                Blog
              </a>
              <a href="/contact" className="text-gray-600 hover:text-gray-900 transition-colors">
                Contact
              </a>
            </div>
            <div className="flex items-center space-x-4">
              <a href="/login">
                <Button variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white">
                  Login
                </Button>
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section ref={heroRef} className={`py-20 bg-gradient-to-br from-blue-50 to-indigo-100 transition-all duration-1000 ${heroVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="bg-blue-100 text-blue-800 mb-6">Platform Features</Badge>
            <h1 className="text-5xl font-bold text-gray-900 mb-6">
              Everything You Need to Excel in Teaching
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Discover the powerful features that make lectur.io the preferred choice for educators worldwide. 
              From curriculum alignment to progress tracking, we've got every aspect of lesson planning covered.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-600">
              <div className="flex items-center">
                <Shield className="h-4 w-4 mr-2 text-blue-600" />
                <span>Standards Compliant</span>
              </div>
              <div className="flex items-center">
                <Zap className="h-4 w-4 mr-2 text-blue-600" />
                <span>Time-Saving</span>
              </div>
              <div className="flex items-center">
                <Download className="h-4 w-4 mr-2 text-blue-600" />
                <span>Ready to Use</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Feature Sections */}
      {features.map((feature) => {
        const IconComponent = feature.icon;
        return (
          <section 
            key={feature.id} 
            ref={feature.ref}
            className={`py-20 transition-all duration-1000 ${feature.isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'} ${feature.id % 2 === 0 ? 'bg-gray-50' : 'bg-white'}`}
          >
            <div className="container mx-auto px-4">
              <div className="max-w-6xl mx-auto">
                <div className={`flex flex-col ${feature.imageLeft ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-12`}>
                  {/* Image Side */}
                  <div className="lg:w-1/2">
                    <div className="relative">
                      <img
                        src={feature.image}
                        alt={feature.imageAlt}
                        className="w-full h-96 object-cover rounded-lg shadow-lg"
                        data-source={feature.image.includes('unsplash') ? "Photo from Unsplash" : "Photo from Pexels"}
                        title={feature.image.includes('unsplash') ? "Photo from Unsplash" : "Photo from Pexels"}
                      />
                      <div className="absolute -bottom-6 -right-6 h-16 w-16 bg-blue-600 rounded-lg flex items-center justify-center shadow-lg">
                        <IconComponent className="h-8 w-8 text-white" />
                      </div>
                    </div>
                  </div>

                  {/* Content Side */}
                  <div className="lg:w-1/2">
                    <div className="max-w-lg">
                      <h2 className="text-3xl font-bold text-gray-900 mb-6">
                        {feature.title}
                      </h2>
                      <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                        {feature.description}
                      </p>
                      
                      <div className="space-y-4 mb-8">
                        {feature.benefits.map((benefit, index) => (
                          <div key={index} className="flex items-center">
                            <div className="h-2 w-2 bg-blue-600 rounded-full mr-3"></div>
                            <span className="text-gray-700">{benefit}</span>
                          </div>
                        ))}
                      </div>

                      <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                        Learn More
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        );
      })}

      {/* CTA Section */}
      <section ref={ctaRef} className={`py-20 bg-blue-600 transition-all duration-1000 ${ctaVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center text-white">
            <h2 className="text-4xl font-bold mb-6">
              Ready to Transform Your Teaching?
            </h2>
            <p className="text-xl text-blue-100 mb-8 leading-relaxed">
              Join thousands of educators who are already saving time and improving student outcomes with lectur.io.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3">
                Start Free Trial
              </Button>
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3">
                View Pricing
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Cookie Banner */}
      <CookieBanner />
    </div>
  );
}
