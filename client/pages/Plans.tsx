import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  BookOpen,
  Search,
  Filter,
  Clock,
  Users,
  Star,
  Download,
  Eye,
  Heart,
} from "lucide-react";
import { useScrollReveal } from "@/hooks/useScrollReveal";

export default function Plans() {
  const plansReveal = useScrollReveal({ threshold: 0.1 });
  const featuredReveal = useScrollReveal({ threshold: 0.1 });

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg flex items-center justify-center">
                <BookOpen className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">lectur.io</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <a
                href="/"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Home
              </a>
              <a
                href="/#features"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Features
              </a>
              <a
                href="/pricing"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Pricing
              </a>
              <a href="/plans" className="text-blue-600 font-semibold">
                Lesson Plans
              </a>
              <a
                href="/#reviews"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Reviews
              </a>
              <Button
                variant="outline"
                className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
              >
                Login
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Discover Amazing Lesson Plans
          </h1>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Browse our extensive library of professionally crafted lesson plans
            across all subjects and grade levels.
          </p>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto mb-8">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search lesson plans..."
                  className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-blue-200 focus-visible:ring-white/50"
                />
              </div>
              <Button
                size="lg"
                className="bg-white text-blue-600 hover:bg-blue-50 font-semibold"
              >
                Search
              </Button>
            </div>
          </div>

          <div className="flex flex-wrap items-center justify-center gap-4 text-sm">
            <Badge className="bg-white/20 text-white border-white/30">
              <BookOpen className="h-3 w-3 mr-1" />
              5,000+ Lesson Plans
            </Badge>
            <Badge className="bg-white/20 text-white border-white/30">
              <Users className="h-3 w-3 mr-1" />
              All Grade Levels
            </Badge>
            <Badge className="bg-white/20 text-white border-white/30">
              <Star className="h-3 w-3 mr-1" />
              Curriculum Aligned
            </Badge>
          </div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-8 bg-gray-50 border-b border-gray-200">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-5 w-5 text-gray-500" />
              <span className="font-medium text-gray-700">Filter by:</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="text-gray-600 border-gray-300"
            >
              All Subjects
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="text-gray-600 border-gray-300"
            >
              Grade Level
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="text-gray-600 border-gray-300"
            >
              Duration
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="text-gray-600 border-gray-300"
            >
              Difficulty
            </Button>
          </div>
        </div>
      </section>

      {/* Featured Plans */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Featured Lesson Plans
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Our most popular and highly-rated lesson plans, loved by educators
              worldwide
            </p>
          </div>

          <div
            ref={featuredReveal.ref}
            className={`grid md:grid-cols-2 lg:grid-cols-3 gap-8 transition-all duration-700 ${
              featuredReveal.isVisible
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-8"
            }`}
          >
            {/* Featured Plan 1 */}
            <Card
              className={`border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-500 overflow-hidden bg-white ${
                featuredReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "100ms" }}
            >
              <div className="h-48 w-full overflow-hidden relative">
                <img
                  src="https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
                  alt="Science experiment with plants"
                  className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                  data-source="Photo by Markus Spiske on Unsplash"
                  title="Photo by Markus Spiske on Unsplash"
                />
                <div className="absolute top-3 left-3">
                  <Badge className="bg-green-100 text-green-700">Science</Badge>
                </div>
                <div className="absolute top-3 right-3">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="bg-white/80 hover:bg-white"
                  >
                    <Heart className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardHeader>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Clock className="h-4 w-4" />
                    <span>45 min</span>
                    <Users className="h-4 w-4 ml-2" />
                    <span>Grade 3-5</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm text-gray-600">4.8</span>
                  </div>
                </div>
                <CardTitle className="text-gray-900">
                  Plant Life Cycles & Growth
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Interactive lesson exploring how plants grow and reproduce
                  through hands-on activities and observations
                </CardDescription>
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Download className="h-4 w-4" />
                      <span>1.2k</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Eye className="h-4 w-4" />
                      <span>3.4k</span>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    View Plan
                  </Button>
                </div>
              </CardHeader>
            </Card>

            {/* Featured Plan 2 */}
            <Card
              className={`border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-500 overflow-hidden bg-white ${
                featuredReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "200ms" }}
            >
              <div className="h-48 w-full overflow-hidden relative">
                <img
                  src="https://images.unsplash.com/photo-1509062522246-3755977927d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
                  alt="Math equations on blackboard"
                  className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                  data-source="Photo by Antoine Dautry on Unsplash"
                  title="Photo by Antoine Dautry on Unsplash"
                />
                <div className="absolute top-3 left-3">
                  <Badge className="bg-blue-100 text-blue-700">
                    Mathematics
                  </Badge>
                </div>
                <div className="absolute top-3 right-3">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="bg-white/80 hover:bg-white"
                  >
                    <Heart className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardHeader>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Clock className="h-4 w-4" />
                    <span>60 min</span>
                    <Users className="h-4 w-4 ml-2" />
                    <span>Grade 4-6</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm text-gray-600">4.9</span>
                  </div>
                </div>
                <CardTitle className="text-gray-900">
                  Fractions Made Easy
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Visual and practical approach to understanding fractions using
                  real-world examples and manipulatives
                </CardDescription>
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Download className="h-4 w-4" />
                      <span>890</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Eye className="h-4 w-4" />
                      <span>2.1k</span>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    View Plan
                  </Button>
                </div>
              </CardHeader>
            </Card>

            {/* Featured Plan 3 */}
            <Card
              className={`border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-500 overflow-hidden bg-white ${
                featuredReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "300ms" }}
            >
              <div className="h-48 w-full overflow-hidden relative">
                <img
                  src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
                  alt="Historical documents and maps"
                  className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                  data-source="Photo by Giammarco Boscaro on Unsplash"
                  title="Photo by Giammarco Boscaro on Unsplash"
                />
                <div className="absolute top-3 left-3">
                  <Badge className="bg-purple-100 text-purple-700">
                    History
                  </Badge>
                </div>
                <div className="absolute top-3 right-3">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="bg-white/80 hover:bg-white"
                  >
                    <Heart className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardHeader>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Clock className="h-4 w-4" />
                    <span>50 min</span>
                    <Users className="h-4 w-4 ml-2" />
                    <span>Grade 6-8</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm text-gray-600">4.7</span>
                  </div>
                </div>
                <CardTitle className="text-gray-900">
                  Ancient Civilizations
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Journey through ancient Egypt, Greece, and Rome with
                  interactive timelines and engaging activities
                </CardDescription>
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Download className="h-4 w-4" />
                      <span>1.5k</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Eye className="h-4 w-4" />
                      <span>4.2k</span>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    View Plan
                  </Button>
                </div>
              </CardHeader>
            </Card>
          </div>

          <div className="text-center mt-12">
            <Button
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3"
            >
              Browse All Lesson Plans
            </Button>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Browse by Subject
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Find lesson plans organized by subject area and grade level
            </p>
          </div>

          <div
            ref={plansReveal.ref}
            className={`grid md:grid-cols-2 lg:grid-cols-4 gap-6 transition-all duration-700 ${
              plansReveal.isVisible
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-8"
            }`}
          >
            <Card className="bg-white hover:shadow-lg transition-all duration-300 cursor-pointer">
              <CardHeader className="text-center">
                <div className="h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle className="text-lg">Science</CardTitle>
                <CardDescription>1,200+ lesson plans</CardDescription>
              </CardHeader>
            </Card>

            <Card className="bg-white hover:shadow-lg transition-all duration-300 cursor-pointer">
              <CardHeader className="text-center">
                <div className="h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="h-8 w-8 text-blue-600" />
                </div>
                <CardTitle className="text-lg">Mathematics</CardTitle>
                <CardDescription>980+ lesson plans</CardDescription>
              </CardHeader>
            </Card>

            <Card className="bg-white hover:shadow-lg transition-all duration-300 cursor-pointer">
              <CardHeader className="text-center">
                <div className="h-16 w-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="h-8 w-8 text-purple-600" />
                </div>
                <CardTitle className="text-lg">History</CardTitle>
                <CardDescription>750+ lesson plans</CardDescription>
              </CardHeader>
            </Card>

            <Card className="bg-white hover:shadow-lg transition-all duration-300 cursor-pointer">
              <CardHeader className="text-center">
                <div className="h-16 w-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="h-8 w-8 text-orange-600" />
                </div>
                <CardTitle className="text-lg">Language Arts</CardTitle>
                <CardDescription>1,100+ lesson plans</CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Transform Your Teaching?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of educators who save hours every week with our
            lesson plans.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <Button
              size="lg"
              className="text-lg px-8 py-6 bg-white text-blue-600 hover:bg-blue-50 shadow-lg hover:shadow-xl transition-all duration-200 font-semibold"
            >
              Start Free Trial
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="text-lg px-8 py-6 border-white text-blue-500 hover:bg-white hover:text-white"
            >
              View Pricing
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
