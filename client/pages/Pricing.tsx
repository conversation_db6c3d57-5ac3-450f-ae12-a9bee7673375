import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, BookOpen, Zap, Users, Crown, Star } from "lucide-react";
import { useScrollReveal } from "@/hooks/useScrollReveal";

export default function Pricing() {
  const pricingReveal = useScrollReveal({ threshold: 0.1 });
  const featuresReveal = useScrollReveal({ threshold: 0.1 });

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg flex items-center justify-center">
                <BookOpen className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">lectur.io</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <a
                href="/"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Home
              </a>
              <a
                href="/#features"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Features
              </a>
              <a href="/pricing" className="text-blue-600 font-semibold">
                Pricing
              </a>
              <a
                href="/#reviews"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Reviews
              </a>
              <Button
                variant="outline"
                className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
              >
                Login
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Choose Your Perfect Plan
          </h1>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Start free and scale as you grow. All plans include unlimited
            downloads and premium support.
          </p>
          <Badge className="bg-white/20 text-white border-white/30">
            <Star className="h-3 w-3 mr-1" />
            30-day money-back guarantee
          </Badge>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div
            ref={pricingReveal.ref}
            className={`grid md:grid-cols-3 gap-8 max-w-6xl mx-auto transition-all duration-700 ${
              pricingReveal.isVisible
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-8"
            }`}
          >
            {/* Starter Plan */}
            <Card
              className={`border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-500 bg-white ${
                pricingReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "100ms" }}
            >
              <CardHeader>
                <CardTitle className="text-xl">Starter</CardTitle>
                <div className="text-3xl font-bold">Free</div>
                <CardDescription>Perfect for trying us out</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />5 lesson
                    plans per month
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Basic templates
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Email support
                  </li>
                </ul>
                <Button
                  variant="outline"
                  className="w-full border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
                >
                  Get Started Free
                </Button>
              </CardContent>
            </Card>

            {/* Professional Plan */}
            <Card
              className={`border-blue-300 ring-2 ring-blue-200 relative bg-white transition-all duration-500 hover:shadow-lg ${
                pricingReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "200ms" }}
            >
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-blue-600 text-white">Most Popular</Badge>
              </div>
              <CardHeader>
                <CardTitle className="text-xl">Professional</CardTitle>
                <div className="text-3xl font-bold">
                  $19
                  <span className="text-lg font-normal text-gray-600">
                    /month
                  </span>
                </div>
                <CardDescription>For dedicated teachers</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Unlimited lesson plans
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Premium templates
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Assessment tools
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Priority support
                  </li>
                </ul>
                <Button className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold">
                  Start 7-Day Trial
                </Button>
              </CardContent>
            </Card>

            {/* School Plan */}
            <Card
              className={`border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-500 bg-white ${
                pricingReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "300ms" }}
            >
              <CardHeader>
                <CardTitle className="text-xl">School</CardTitle>
                <div className="text-3xl font-bold">
                  $99
                  <span className="text-lg font-normal text-gray-600">
                    /month
                  </span>
                </div>
                <CardDescription>For entire schools</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Everything in Professional
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Up to 50 teachers
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Admin dashboard
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Custom branding
                  </li>
                </ul>
                <Button
                  variant="outline"
                  className="w-full border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
                >
                  Contact Sales
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Features Comparison */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Compare All Features
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See exactly what's included in each plan
            </p>
          </div>

          <div
            ref={featuresReveal.ref}
            className={`max-w-4xl mx-auto transition-all duration-700 ${
              featuresReveal.isVisible
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-8"
            }`}
          >
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="grid grid-cols-4 bg-gray-50 border-b border-gray-200">
                <div className="p-4 font-semibold text-gray-900">Features</div>
                <div className="p-4 text-center font-semibold text-gray-900">
                  Starter
                </div>
                <div className="p-4 text-center font-semibold text-blue-600">
                  Professional
                </div>
                <div className="p-4 text-center font-semibold text-gray-900">
                  School
                </div>
              </div>

              <div className="grid grid-cols-4 border-b border-gray-100">
                <div className="p-4 text-gray-700">Lesson Plans</div>
                <div className="p-4 text-center text-gray-600">5/month</div>
                <div className="p-4 text-center text-green-600">Unlimited</div>
                <div className="p-4 text-center text-green-600">Unlimited</div>
              </div>

              <div className="grid grid-cols-4 border-b border-gray-100 bg-gray-25">
                <div className="p-4 text-gray-700">Premium Templates</div>
                <div className="p-4 text-center text-red-500">✗</div>
                <div className="p-4 text-center text-green-600">✓</div>
                <div className="p-4 text-center text-green-600">✓</div>
              </div>

              <div className="grid grid-cols-4 border-b border-gray-100">
                <div className="p-4 text-gray-700">Assessment Tools</div>
                <div className="p-4 text-center text-red-500">✗</div>
                <div className="p-4 text-center text-green-600">✓</div>
                <div className="p-4 text-center text-green-600">✓</div>
              </div>

              <div className="grid grid-cols-4 border-b border-gray-100 bg-gray-25">
                <div className="p-4 text-gray-700">Priority Support</div>
                <div className="p-4 text-center text-red-500">✗</div>
                <div className="p-4 text-center text-green-600">✓</div>
                <div className="p-4 text-center text-green-600">✓</div>
              </div>

              <div className="grid grid-cols-4 border-b border-gray-100">
                <div className="p-4 text-gray-700">Admin Dashboard</div>
                <div className="p-4 text-center text-red-500">✗</div>
                <div className="p-4 text-center text-red-500">✗</div>
                <div className="p-4 text-center text-green-600">✓</div>
              </div>

              <div className="grid grid-cols-4 bg-gray-25">
                <div className="p-4 text-gray-700">Custom Branding</div>
                <div className="p-4 text-center text-red-500">✗</div>
                <div className="p-4 text-center text-red-500">✗</div>
                <div className="p-4 text-center text-green-600">✓</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
          </div>

          <div className="max-w-3xl mx-auto space-y-6">
            <Card className="bg-white">
              <CardHeader>
                <CardTitle className="text-lg">
                  Can I change plans anytime?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Yes, you can upgrade or downgrade your plan at any time.
                  Changes take effect immediately.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white">
              <CardHeader>
                <CardTitle className="text-lg">
                  Is there a free trial?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Yes! Professional and School plans come with a 7-day free
                  trial. No credit card required.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white">
              <CardHeader>
                <CardTitle className="text-lg">
                  What's included in the money-back guarantee?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  If you're not satisfied within 30 days, we'll refund your
                  payment in full, no questions asked.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of educators who trust lectur.io for their lesson
            planning needs.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <Button
              size="lg"
              className="text-lg px-8 py-6 bg-white text-blue-600 hover:bg-blue-50 shadow-lg hover:shadow-xl transition-all duration-200 font-semibold"
            >
              <Zap className="h-5 w-5 mr-2" />
              Start Free Trial
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="text-lg px-8 py-6 border-white text-white hover:bg-white hover:text-blue-600"
            >
              Contact Sales
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
