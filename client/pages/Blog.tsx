import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  BookOpen,
  Calendar,
  Clock,
  User,
  Search,
  ArrowRight,
  TrendingUp,
  Users,
  Lightbulb,
} from "lucide-react";
import { useScrollReveal } from "@/hooks/useScrollReveal";
import { CookieBanner } from "@/components/CookieBanner";

export default function Blog() {
  const { ref: heroRef, isVisible: heroVisible } = useScrollReveal();
  const { ref: postsRef, isVisible: postsVisible } = useScrollReveal();
  const { ref: categoriesRef, isVisible: categoriesVisible } =
    useScrollReveal();

  const featuredPost = {
    id: 1,
    title:
      "10 Innovative Teaching Strategies That Transform Student Engagement",
    excerpt:
      "Discover proven methods to captivate your students and create memorable learning experiences that stick. From interactive storytelling to gamification techniques.",
    author: "Dr. <PERSON>",
    date: "December 15, 2024",
    readTime: "8 min read",
    category: "Teaching Strategies",
    image:
      "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
    featured: true,
  };

  const blogPosts = [
    {
      id: 2,
      title: "The Science Behind Effective Lesson Planning",
      excerpt:
        "Learn how cognitive science principles can improve your lesson design and student outcomes.",
      author: "Prof. Michael Chen",
      date: "December 12, 2024",
      readTime: "6 min read",
      category: "Educational Research",
      image:
        "https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
    },
    {
      id: 3,
      title: "Digital Tools Every Modern Educator Should Know",
      excerpt:
        "A comprehensive guide to the latest educational technology that's making a real difference in classrooms.",
      author: "Lisa Rodriguez",
      date: "December 10, 2024",
      readTime: "5 min read",
      category: "EdTech",
      image:
        "https://images.unsplash.com/photo-1509062522246-3755977927d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
    },
    {
      id: 4,
      title: "Building Inclusive Classrooms: A Practical Guide",
      excerpt:
        "Strategies for creating learning environments where every student feels valued and supported.",
      author: "Dr. Amanda Williams",
      date: "December 8, 2024",
      readTime: "7 min read",
      category: "Inclusive Education",
      image:
        "https://images.pexels.com/photos/8617981/pexels-photo-8617981.jpeg",
    },
    {
      id: 5,
      title: "Assessment Strategies That Actually Work",
      excerpt:
        "Move beyond traditional testing with these innovative assessment methods that provide real insights.",
      author: "James Thompson",
      date: "December 5, 2024",
      readTime: "6 min read",
      category: "Assessment",
      image:
        "https://images.pexels.com/photos/8617961/pexels-photo-8617961.jpeg",
    },
    {
      id: 6,
      title: "The Future of Education: Trends to Watch in 2025",
      excerpt:
        "Explore emerging trends that will shape the educational landscape in the coming year.",
      author: "Dr. Robert Kim",
      date: "December 3, 2024",
      readTime: "9 min read",
      category: "Future of Education",
      image:
        "https://images.pexels.com/photos/8199252/pexels-photo-8199252.jpeg",
    },
  ];

  const categories = [
    { name: "Teaching Strategies", count: 24, icon: Lightbulb },
    { name: "Educational Research", count: 18, icon: TrendingUp },
    { name: "EdTech", count: 15, icon: BookOpen },
    { name: "Inclusive Education", count: 12, icon: Users },
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <a
              href="/"
              className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
            >
              <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg flex items-center justify-center">
                <BookOpen className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">lectur.io</span>
            </a>
            <div className="hidden md:flex items-center justify-center space-x-8 flex-1">
              <a
                href="/"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Home
              </a>
              <a
                href="/features"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Features
              </a>
              <a
                href="/plans"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Lesson Plans
              </a>
              <a
                href="/pricing"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Pricing
              </a>
              <a
                href="/reviews"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Reviews
              </a>
              <a href="/blog" className="text-blue-600 font-medium">
                Blog
              </a>
              <a
                href="/contact"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Contact
              </a>
            </div>
            <div className="flex items-center space-x-4">
              <a href="/login">
                <Button
                  variant="outline"
                  className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
                >
                  Login
                </Button>
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section
        ref={heroRef}
        className={`py-20 bg-gradient-to-br from-blue-50 to-indigo-100 transition-all duration-1000 ${heroVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"}`}
      >
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl font-bold text-gray-900 mb-6">
              Educational Insights & Resources
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Stay updated with the latest teaching strategies, educational
              research, and classroom innovations from leading educators around
              the world.
            </p>

            {/* Search Bar */}
            <div className="max-w-md mx-auto relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                placeholder="Search articles..."
                className="pl-10 h-12 text-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Featured Post */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="mb-8">
              <Badge className="bg-blue-100 text-blue-800 mb-4">
                Featured Article
              </Badge>
              <h2 className="text-3xl font-bold text-gray-900">
                Editor's Pick
              </h2>
            </div>

            <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
              <div className="md:flex">
                <div className="md:w-1/2">
                  <img
                    src={featuredPost.image}
                    alt={featuredPost.title}
                    className="h-64 md:h-full w-full object-cover"
                    data-source="Photo by Annie Spratt on Unsplash"
                    title="Photo by Annie Spratt on Unsplash"
                  />
                </div>
                <div className="md:w-1/2 p-8">
                  <Badge className="bg-blue-100 text-blue-800 mb-4">
                    {featuredPost.category}
                  </Badge>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    {featuredPost.title}
                  </h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {featuredPost.excerpt}
                  </p>

                  <div className="flex items-center text-sm text-gray-500 mb-6">
                    <User className="h-4 w-4 mr-2" />
                    <span className="mr-4">{featuredPost.author}</span>
                    <Calendar className="h-4 w-4 mr-2" />
                    <span className="mr-4">{featuredPost.date}</span>
                    <Clock className="h-4 w-4 mr-2" />
                    <span>{featuredPost.readTime}</span>
                  </div>

                  <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                    Read Article
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section
        ref={postsRef}
        className={`py-16 bg-gray-50 transition-all duration-1000 ${postsVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"}`}
      >
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex justify-between items-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900">
                Latest Articles
              </h2>
              <Button
                variant="outline"
                className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
              >
                View All Posts
              </Button>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {blogPosts.map((post) => (
                <Card
                  key={post.id}
                  className="overflow-hidden hover:shadow-lg transition-shadow duration-300 bg-white"
                >
                  <div className="h-48 overflow-hidden">
                    <img
                      src={post.image}
                      alt={post.title}
                      className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                      data-source={
                        post.image.includes("unsplash")
                          ? "Photo from Unsplash"
                          : "Photo from Pexels"
                      }
                      title={
                        post.image.includes("unsplash")
                          ? "Photo from Unsplash"
                          : "Photo from Pexels"
                      }
                    />
                  </div>
                  <CardContent className="p-6">
                    <Badge className="bg-blue-100 text-blue-800 mb-3">
                      {post.category}
                    </Badge>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>

                    <div className="flex items-center text-sm text-gray-500 mb-4">
                      <User className="h-4 w-4 mr-1" />
                      <span className="mr-3">{post.author}</span>
                      <Clock className="h-4 w-4 mr-1" />
                      <span>{post.readTime}</span>
                    </div>

                    <Button
                      variant="ghost"
                      className="text-blue-600 hover:text-blue-700 p-0"
                    >
                      Read More
                      <ArrowRight className="ml-1 h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section
        ref={categoriesRef}
        className={`py-16 transition-all duration-1000 ${categoriesVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"}`}
      >
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Explore by Category
            </h2>
            <p className="text-gray-600 mb-12">
              Find articles that match your interests and teaching needs
            </p>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {categories.map((category) => {
                const IconComponent = category.icon;
                return (
                  <Card
                    key={category.name}
                    className="p-6 hover:shadow-lg transition-shadow duration-300 cursor-pointer"
                  >
                    <div className="text-center">
                      <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <IconComponent className="h-6 w-6 text-blue-600" />
                      </div>
                      <h3 className="font-semibold text-gray-900 mb-2">
                        {category.name}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {category.count} articles
                      </p>
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-16 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center text-white">
            <h2 className="text-3xl font-bold mb-4">Stay Updated</h2>
            <p className="text-blue-100 mb-8">
              Get the latest educational insights and teaching resources
              delivered to your inbox weekly.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                placeholder="Enter your email"
                className="flex-1 bg-white text-gray-900"
              />
              <Button className="bg-white text-blue-600 hover:bg-gray-100">
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Cookie Banner */}
      <CookieBanner />
    </div>
  );
}
