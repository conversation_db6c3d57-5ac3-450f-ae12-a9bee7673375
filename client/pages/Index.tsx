import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { useScrollReveal } from "@/hooks/useScrollReveal";
import {
  Check,
  BookOpen,
  Users,
  Clock,
  Star,
  Download,
  Sparkles,
  Target,
  Zap,
} from "lucide-react";

export default function Index() {
  const featuresReveal = useScrollReveal({ threshold: 0.1 });
  const pricingReveal = useScrollReveal({ threshold: 0.1 });
  const reviewsReveal = useScrollReveal({ threshold: 0.1 });

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section with Blue Background */}
      <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700 text-white min-h-screen flex flex-col">
        {/* Navigation */}
        <nav className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-white rounded-lg flex items-center justify-center">
                <BookOpen className="h-5 w-5 text-blue-600" />
              </div>
              <span className="text-xl font-bold text-white">lectur.io</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <a
                href="#features"
                className="text-blue-100 hover:text-white transition-colors"
              >
                Features
              </a>
              <a
                href="#pricing"
                className="text-blue-100 hover:text-white transition-colors"
              >
                Pricing
              </a>
              <a
                href="#reviews"
                className="text-blue-100 hover:text-white transition-colors"
              >
                Reviews
              </a>
              <Button
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600 bg-blue-600"
              >
                Login
              </Button>
            </div>
          </div>
        </nav>

        {/* Hero Content */}
        <div className="container mx-auto px-4 flex-1 flex items-center justify-center">
          <div className="text-center max-w-4xl mx-auto">
            <Badge className="mb-6 bg-white/20 text-white hover:bg-white/30 border-white/30">
              <Sparkles className="h-3 w-3 mr-1" />
              Trusted by 50,000+ educators
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
              Lesson Plans That
              <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                {" "}
                Transform{" "}
              </span>
              Learning
            </h1>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto leading-relaxed">
              Save 10+ hours per week with our professionally designed,
              curriculum-aligned lesson plans. Ready to use, easy to customize,
              and loved by teachers worldwide.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12">
              <Button
                size="lg"
                className="text-lg px-8 py-6 bg-white text-blue-600 hover:bg-blue-50 shadow-lg hover:shadow-xl transition-all duration-200 font-semibold"
              >
                <Zap className="h-5 w-5 mr-2" />
                Start Free Trial
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-6 border-white text-white hover:bg-white hover:text-blue-600 bg-blue-600"
              >
                <BookOpen className="h-5 w-5 mr-2" />
                Browse Plans
              </Button>
            </div>
            <div className="flex items-center justify-center space-x-8 text-sm text-blue-100">
              <div className="flex items-center">
                <Check className="h-4 w-4 text-green-300 mr-2" />
                7-day free trial
              </div>
              <div className="flex items-center">
                <Check className="h-4 w-4 text-green-300 mr-2" />
                Cancel anytime
              </div>
              <div className="flex items-center">
                <Check className="h-4 w-4 text-green-300 mr-2" />
                No setup fees
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="bg-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need to Excel
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              From detailed lesson plans to assessment tools, we've got your
              teaching covered.
            </p>
          </div>
          <div
            ref={featuresReveal.ref}
            className={`grid md:grid-cols-3 gap-8 transition-all duration-700 ${
              featuresReveal.isVisible
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-8"
            }`}
          >
            <Card
              className={`border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-500 overflow-hidden bg-white ${
                featuresReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "100ms" }}
            >
              <div className="h-48 w-full overflow-hidden">
                <img
                  src="https://images.pexels.com/photos/8617981/pexels-photo-8617981.jpeg"
                  alt="Teacher pointing at globe in classroom"
                  className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                />
              </div>
              <CardHeader>
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <Target className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle className="text-gray-900">
                  Curriculum Aligned
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Every lesson plan meets national and state standards, saving
                  you research time.
                </CardDescription>
              </CardHeader>
            </Card>
            <Card
              className={`border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-500 overflow-hidden bg-white ${
                featuresReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "200ms" }}
            >
              <div className="h-48 w-full overflow-hidden">
                <img
                  src="https://images.pexels.com/photos/8617961/pexels-photo-8617961.jpeg"
                  alt="Students listening to teacher in bright classroom"
                  className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                />
              </div>
              <CardHeader>
                <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                  <Clock className="h-6 w-6 text-orange-600" />
                </div>
                <CardTitle className="text-gray-900">Time-Saving</CardTitle>
                <CardDescription className="text-gray-600">
                  Pre-built activities, assessments, and materials that work
                  right out of the box.
                </CardDescription>
              </CardHeader>
            </Card>
            <Card
              className={`border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-500 overflow-hidden bg-white ${
                featuresReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "300ms" }}
            >
              <div className="h-48 w-full overflow-hidden">
                <img
                  src="https://images.pexels.com/photos/8199252/pexels-photo-8199252.jpeg"
                  alt="Woman typing on laptop in library setting"
                  className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                />
              </div>
              <CardHeader>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                  <Download className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle className="text-gray-900">
                  Instantly Accessible
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Download plans in multiple formats - PDF, Word, or Google Docs
                  compatible.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Choose Your Plan
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Start free and upgrade when you're ready. All plans include
              unlimited downloads.
            </p>
          </div>
          <div
            ref={pricingReveal.ref}
            className={`grid md:grid-cols-3 gap-8 max-w-5xl mx-auto transition-all duration-700 ${
              pricingReveal.isVisible
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-8"
            }`}
          >
            {/* Starter Plan */}
            <Card
              className={`border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-500 bg-white ${
                pricingReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "100ms" }}
            >
              <CardHeader>
                <CardTitle className="text-xl">Starter</CardTitle>
                <div className="text-3xl font-bold">Free</div>
                <CardDescription>Perfect for trying us out</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />5 lesson
                    plans per month
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Basic templates
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Email support
                  </li>
                </ul>
                <Button
                  variant="outline"
                  className="w-full border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
                >
                  Get Started Free
                </Button>
              </CardContent>
            </Card>

            {/* Professional Plan */}
            <Card
              className={`border-blue-300 ring-2 ring-blue-200 relative bg-white transition-all duration-500 hover:shadow-lg ${
                pricingReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "200ms" }}
            >
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-purple-600 text-white">Most Popular</Badge>
              </div>
              <CardHeader>
                <CardTitle className="text-xl">Professional</CardTitle>
                <div className="text-3xl font-bold">
                  $19
                  <span className="text-lg font-normal text-muted-foreground">
                    /month
                  </span>
                </div>
                <CardDescription>For dedicated teachers</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Unlimited lesson plans
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Premium templates
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Assessment tools
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Priority support
                  </li>
                </ul>
                <Button className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold">
                  Start 7-Day Trial
                </Button>
              </CardContent>
            </Card>

            {/* School Plan */}
            <Card
              className={`border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-500 bg-white ${
                pricingReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "300ms" }}
            >
              <CardHeader>
                <CardTitle className="text-xl">School</CardTitle>
                <div className="text-3xl font-bold">
                  $99
                  <span className="text-lg font-normal text-muted-foreground">
                    /month
                  </span>
                </div>
                <CardDescription>For entire schools</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Everything in Professional
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Up to 50 teachers
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Admin dashboard
                  </li>
                  <li className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    Custom branding
                  </li>
                </ul>
                <Button
                  variant="outline"
                  className="w-full border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
                >
                  Contact Sales
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="reviews" className="bg-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Loved by Teachers
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See what educators are saying about lectur.io
            </p>
          </div>
          <div
            ref={reviewsReveal.ref}
            className={`grid md:grid-cols-3 gap-8 max-w-5xl mx-auto transition-all duration-700 ${
              reviewsReveal.isVisible
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-8"
            }`}
          >
            <Card
              className={`border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-500 bg-white ${
                reviewsReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "100ms" }}
            >
              <CardContent className="pt-6">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="h-4 w-4 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                </div>
                <p className="text-muted-foreground mb-4">
                  "lectur.io has completely transformed how I prepare for class.
                  I save hours every week and my students are more engaged than
                  ever."
                </p>
                <div className="flex items-center">
                  <div className="h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                    <Users className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="font-semibold">Sarah Johnson</p>
                    <p className="text-sm text-muted-foreground">
                      5th Grade Teacher
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card
              className={`border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-500 bg-white ${
                reviewsReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "200ms" }}
            >
              <CardContent className="pt-6">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="h-4 w-4 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                </div>
                <p className="text-muted-foreground mb-4">
                  "The quality of the lesson plans is outstanding. Everything is
                  well-researched and perfectly aligned with our curriculum
                  standards."
                </p>
                <div className="flex items-center">
                  <div className="h-10 w-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                    <Users className="h-5 w-5 text-orange-600" />
                  </div>
                  <div>
                    <p className="font-semibold">Mike Chen</p>
                    <p className="text-sm text-muted-foreground">
                      High School Biology
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card
              className={`border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-500 bg-white ${
                reviewsReveal.isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
              }`}
              style={{ transitionDelay: "300ms" }}
            >
              <CardContent className="pt-6">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="h-4 w-4 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                </div>
                <p className="text-muted-foreground mb-4">
                  "As a new teacher, lectur.io gave me the confidence I needed.
                  The plans are detailed but flexible enough to make my own."
                </p>
                <div className="flex items-center">
                  <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <Users className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="font-semibold">Emily Rodriguez</p>
                    <p className="text-sm text-muted-foreground">
                      2nd Grade Teacher
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-2xl p-8 md:p-16 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Ready to Transform Your Teaching?
            </h2>
            <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
              Join thousands of teachers who have already saved hundreds of
              hours with lectur.io.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Button
                size="lg"
                className="text-lg px-8 py-6 bg-white text-blue-700 hover:bg-blue-50 font-semibold shadow-lg"
              >
                <Zap className="h-5 w-5 mr-2" />
                Start Your Free Trial
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-6 border-white text-white hover:bg-white hover:text-blue-700"
              >
                Schedule Demo
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Stay Updated with Latest Education Trends
            </h2>
            <p className="text-xl mb-8 text-blue-100">
              Get exclusive lesson plans, teaching tips, and educational
              resources delivered to your inbox weekly.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-blue-200 focus-visible:ring-white/50"
              />
              <Button
                size="lg"
                className="bg-white text-blue-700 hover:bg-blue-50 font-semibold shadow-lg"
              >
                Subscribe
              </Button>
            </div>
            <p className="text-sm text-blue-200 mt-4">
              Join 25,000+ educators. Unsubscribe at any time.
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
                <span className="text-xl font-bold text-white">lectur.io</span>
              </div>
              <p className="text-gray-400">
                Empowering educators with professional lesson plans and teaching
                resources.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4 text-white">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Lesson Plans
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Templates
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Assessments
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Resources
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4 text-white">Support</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Contact Us
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Training
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Community
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4 text-white">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    About
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Careers
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Privacy
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; 2024 lectur.io. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
