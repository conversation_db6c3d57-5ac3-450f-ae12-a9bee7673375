import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { BookOpen, Star, Quote, Users, Award, TrendingUp } from "lucide-react";
import { useScrollReveal } from "@/hooks/useScrollReveal";
import { CookieBanner } from "@/components/CookieBanner";

export default function Reviews() {
  const { ref: heroRef, isVisible: heroVisible } = useScrollReveal();
  const { ref: statsRef, isVisible: statsVisible } = useScrollReveal();
  const { ref: reviewsRef, isVisible: reviewsVisible } = useScrollReveal();
  const { ref: ctaRef, isVisible: ctaVisible } = useScrollReveal();

  const stats = [
    {
      icon: Users,
      number: "50,000+",
      label: "Happy Educators",
      description: "Teachers worldwide trust lectur.io",
    },
    {
      icon: Star,
      number: "4.9/5",
      label: "Average Rating",
      description: "Based on 12,000+ reviews",
    },
    {
      icon: Award,
      number: "98%",
      label: "Satisfaction Rate",
      description: "Would recommend to colleagues",
    },
    {
      icon: TrendingUp,
      number: "10+",
      label: "Hours Saved",
      description: "Average time saved per week",
    },
  ];

  const reviews = [
    {
      id: 1,
      name: "Sarah Johnson",
      role: "5th Grade Teacher",
      school: "Lincoln Elementary School",
      rating: 5,
      review:
        "Lectur.io has completely transformed my teaching experience. The lesson plans are incredibly detailed and align perfectly with our curriculum standards. I've saved at least 15 hours per week on planning, which I can now spend on actually teaching and connecting with my students.",
      avatar: "SJ",
    },
    {
      id: 2,
      name: "Michael Chen",
      role: "High School Science Teacher",
      school: "Roosevelt High School",
      rating: 5,
      review:
        "The interactive activities and experiments included in the science lesson plans are phenomenal. My students are more engaged than ever, and their test scores have improved significantly since I started using these resources.",
      avatar: "MC",
    },
    {
      id: 3,
      name: "Emily Rodriguez",
      role: "Middle School Math Teacher",
      school: "Washington Middle School",
      rating: 5,
      review:
        "As a new teacher, lectur.io has been a lifesaver. The step-by-step lesson plans give me confidence in the classroom, and the assessment tools help me track my students' progress effectively. Highly recommended!",
      avatar: "ER",
    },
    {
      id: 4,
      name: "David Thompson",
      role: "Elementary Principal",
      school: "Jefferson Elementary",
      rating: 5,
      review:
        "We implemented lectur.io across our entire school, and the results have been outstanding. Our teachers are more confident, students are more engaged, and our standardized test scores have improved by 23%.",
      avatar: "DT",
    },
    {
      id: 5,
      name: "Lisa Park",
      role: "Special Education Teacher",
      school: "Madison Elementary",
      rating: 5,
      review:
        "The customizable nature of these lesson plans is perfect for special education. I can easily adapt activities to meet the diverse needs of my students while maintaining high educational standards.",
      avatar: "LP",
    },
    {
      id: 6,
      name: "Robert Wilson",
      role: "High School History Teacher",
      school: "Kennedy High School",
      rating: 5,
      review:
        "The historical accuracy and engaging activities in the history lesson plans are exceptional. My students actually look forward to history class now, and their critical thinking skills have improved dramatically.",
      avatar: "RW",
    },
  ];

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <a
              href="/"
              className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
            >
              <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg flex items-center justify-center">
                <BookOpen className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">lectur.io</span>
            </a>
            <div className="hidden md:flex items-center space-x-8">
              <a
                href="/"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Home
              </a>
              <a
                href="/features"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Features
              </a>
              <a
                href="/plans"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Lesson Plans
              </a>
              <a
                href="/pricing"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Pricing
              </a>
              <a href="/reviews" className="text-blue-600 font-medium">
                Reviews
              </a>
              <a
                href="/blog"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Blog
              </a>
              <a
                href="/contact"
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Contact
              </a>
            </div>
            <div className="flex items-center space-x-4">
              <a href="/login">
                <Button
                  variant="outline"
                  className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
                >
                  Login
                </Button>
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section
        ref={heroRef}
        className={`py-20 bg-gradient-to-br from-blue-50 to-indigo-100 transition-all duration-1000 ${heroVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"}`}
      >
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="bg-blue-100 text-blue-800 mb-6">
              Trusted by Educators
            </Badge>
            <h1 className="text-5xl font-bold text-gray-900 mb-6">
              What Educators Are Saying
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Don't just take our word for it. See what thousands of teachers,
              principals, and education professionals have to say about their
              experience with lectur.io.
            </p>
            <div className="flex items-center justify-center space-x-2 text-lg">
              <div className="flex">{renderStars(5)}</div>
              <span className="font-semibold text-gray-900">4.9 out of 5</span>
              <span className="text-gray-600">from 12,000+ reviews</span>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section
        ref={statsRef}
        className={`py-16 transition-all duration-1000 ${statsVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"}`}
      >
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div key={index} className="text-center">
                    <div className="h-16 w-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="h-8 w-8 text-blue-600" />
                    </div>
                    <div className="text-3xl font-bold text-gray-900 mb-2">
                      {stat.number}
                    </div>
                    <div className="text-lg font-semibold text-gray-700 mb-1">
                      {stat.label}
                    </div>
                    <div className="text-sm text-gray-600">
                      {stat.description}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Reviews Grid */}
      <section
        ref={reviewsRef}
        className={`py-16 bg-gray-50 transition-all duration-1000 ${reviewsVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"}`}
      >
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Real Stories from Real Educators
              </h2>
              <p className="text-gray-600">
                Hear directly from teachers and administrators who have
                transformed their classrooms with lectur.io
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {reviews.map((review) => (
                <Card
                  key={review.id}
                  className="bg-white hover:shadow-lg transition-shadow duration-300"
                >
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                        {review.avatar}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">
                          {review.name}
                        </h3>
                        <p className="text-sm text-gray-600">{review.role}</p>
                        <p className="text-xs text-gray-500">{review.school}</p>
                      </div>
                    </div>

                    <div className="flex mb-4">
                      {renderStars(review.rating)}
                    </div>

                    <div className="relative">
                      <Quote className="absolute -top-2 -left-2 h-6 w-6 text-blue-200" />
                      <p className="text-gray-700 leading-relaxed pl-4">
                        {review.review}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section
        ref={ctaRef}
        className={`py-20 bg-blue-600 transition-all duration-1000 ${ctaVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"}`}
      >
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center text-white">
            <h2 className="text-4xl font-bold mb-6">
              Join Thousands of Satisfied Educators
            </h2>
            <p className="text-xl text-blue-100 mb-8 leading-relaxed">
              Experience the difference that quality lesson plans can make in
              your classroom. Start your free trial today and see why educators
              love lectur.io.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3">
                Start Free Trial
              </Button>
              <Button
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600 bg-blue-600 px-8 py-3"
              >
                View Lesson Plans
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Cookie Banner */}
      <CookieBanner />
    </div>
  );
}
