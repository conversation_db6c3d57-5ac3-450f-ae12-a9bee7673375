@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /**
   * Tailwind CSS theme
   * tailwind.config.ts expects the following color variables to be expressed as HSL values.
   * A different format will require also updating the theme in tailwind.config.ts.
  */
  :root {
    --background: 0 0% 100%;
    --foreground: 226 70% 15%;

    --card: 0 0% 100%;
    --card-foreground: 226 70% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 226 70% 15%;

    --primary: 247 84% 57%;
    --primary-foreground: 0 0% 100%;

    --secondary: 39 100% 97%;
    --secondary-foreground: 226 70% 15%;

    --muted: 220 14% 96%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 39 100% 97%;
    --accent-foreground: 226 70% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 247 84% 57%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 220 13% 4%;
    --foreground: 220 14% 96%;

    --card: 220 13% 6%;
    --card-foreground: 220 14% 96%;

    --popover: 220 13% 6%;
    --popover-foreground: 220 14% 96%;

    --primary: 247 84% 57%;
    --primary-foreground: 0 0% 100%;

    --secondary: 226 50% 16%;
    --secondary-foreground: 220 14% 96%;

    --muted: 226 50% 16%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 226 50% 16%;
    --accent-foreground: 220 14% 96%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 226 50% 16%;
    --input: 226 50% 16%;
    --ring: 247 84% 57%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
