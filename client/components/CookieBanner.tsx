import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { <PERSON>, <PERSON><PERSON>, Settings } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export function CookieBanner() {
  const [isVisible, setIsVisible] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [preferences, setPreferences] = useState({
    necessary: true, // Always true, can't be disabled
    analytics: false,
    marketing: false,
    functional: false,
  });

  useEffect(() => {
    // Check if user has already made a choice
    const cookieConsent = localStorage.getItem("cookieConsent");
    if (!cookieConsent) {
      // Show banner after a short delay
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAcceptAll = () => {
    const consent = {
      necessary: true,
      analytics: true,
      marketing: true,
      functional: true,
      timestamp: new Date().toISOString(),
    };
    localStorage.setItem("cookieConsent", JSON.stringify(consent));
    setIsVisible(false);
    // Here you would initialize your analytics, marketing tools, etc.
    console.log("All cookies accepted:", consent);
  };

  const handleRejectAll = () => {
    const consent = {
      necessary: true,
      analytics: false,
      marketing: false,
      functional: false,
      timestamp: new Date().toISOString(),
    };
    localStorage.setItem("cookieConsent", JSON.stringify(consent));
    setIsVisible(false);
    console.log("Only necessary cookies accepted:", consent);
  };

  const handleSavePreferences = () => {
    const consent = {
      ...preferences,
      timestamp: new Date().toISOString(),
    };
    localStorage.setItem("cookieConsent", JSON.stringify(consent));
    setIsVisible(false);
    setShowSettings(false);
    console.log("Custom preferences saved:", consent);
  };

  const handleClose = () => {
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 z-50 p-4">
        <Card className="bg-white border border-gray-200 shadow-lg max-w-6xl mx-auto">
          <div className="p-6">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <Cookie className="h-6 w-6 text-blue-600 mt-1" />
              </div>
              
              <div className="flex-1 min-w-0">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  We use cookies to enhance your experience
                </h3>
                <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                  We use cookies and similar technologies to provide the best experience on our website. 
                  Some are necessary for the site to function, while others help us understand how you use our site 
                  and improve your experience. You can choose which cookies to accept.
                </p>
                
                <div className="flex flex-wrap gap-3">
                  <Button
                    onClick={handleAcceptAll}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6"
                  >
                    Accept All
                  </Button>
                  
                  <Button
                    onClick={handleRejectAll}
                    variant="outline"
                    className="border-gray-300 text-gray-700 hover:bg-gray-50 px-6"
                  >
                    Reject All
                  </Button>
                  
                  <Dialog open={showSettings} onOpenChange={setShowSettings}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        className="border-gray-300 text-gray-700 hover:bg-gray-50 px-6"
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Customize
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                          <Cookie className="h-5 w-5 text-blue-600" />
                          Cookie Preferences
                        </DialogTitle>
                        <DialogDescription>
                          Choose which cookies you want to accept. You can change these settings at any time.
                        </DialogDescription>
                      </DialogHeader>
                      
                      <div className="space-y-6 py-4">
                        <div className="space-y-4">
                          {/* Necessary Cookies */}
                          <div className="flex items-start justify-between p-4 border border-gray-200 rounded-lg bg-gray-50">
                            <div className="flex-1">
                              <h4 className="font-medium text-gray-900">Necessary Cookies</h4>
                              <p className="text-sm text-gray-600 mt-1">
                                Essential for the website to function properly. These cannot be disabled.
                              </p>
                            </div>
                            <div className="ml-4">
                              <input
                                type="checkbox"
                                checked={true}
                                disabled
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                            </div>
                          </div>

                          {/* Analytics Cookies */}
                          <div className="flex items-start justify-between p-4 border border-gray-200 rounded-lg">
                            <div className="flex-1">
                              <h4 className="font-medium text-gray-900">Analytics Cookies</h4>
                              <p className="text-sm text-gray-600 mt-1">
                                Help us understand how visitors interact with our website by collecting anonymous information.
                              </p>
                            </div>
                            <div className="ml-4">
                              <input
                                type="checkbox"
                                checked={preferences.analytics}
                                onChange={(e) => setPreferences({ ...preferences, analytics: e.target.checked })}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                            </div>
                          </div>

                          {/* Marketing Cookies */}
                          <div className="flex items-start justify-between p-4 border border-gray-200 rounded-lg">
                            <div className="flex-1">
                              <h4 className="font-medium text-gray-900">Marketing Cookies</h4>
                              <p className="text-sm text-gray-600 mt-1">
                                Used to deliver personalized advertisements and track the effectiveness of our marketing campaigns.
                              </p>
                            </div>
                            <div className="ml-4">
                              <input
                                type="checkbox"
                                checked={preferences.marketing}
                                onChange={(e) => setPreferences({ ...preferences, marketing: e.target.checked })}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                            </div>
                          </div>

                          {/* Functional Cookies */}
                          <div className="flex items-start justify-between p-4 border border-gray-200 rounded-lg">
                            <div className="flex-1">
                              <h4 className="font-medium text-gray-900">Functional Cookies</h4>
                              <p className="text-sm text-gray-600 mt-1">
                                Enable enhanced functionality and personalization, such as remembering your preferences.
                              </p>
                            </div>
                            <div className="ml-4">
                              <input
                                type="checkbox"
                                checked={preferences.functional}
                                onChange={(e) => setPreferences({ ...preferences, functional: e.target.checked })}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-3 pt-4 border-t">
                        <Button
                          onClick={handleSavePreferences}
                          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          Save Preferences
                        </Button>
                        <Button
                          onClick={() => setShowSettings(false)}
                          variant="outline"
                          className="px-6"
                        >
                          Cancel
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
              
              <Button
                onClick={handleClose}
                variant="ghost"
                size="sm"
                className="flex-shrink-0 h-8 w-8 p-0 hover:bg-gray-100"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </>
  );
}
