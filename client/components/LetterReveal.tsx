import { useEffect, useState } from "react";

interface LetterRevealProps {
  text: string;
  className?: string;
  delay?: number;
  letterDelay?: number;
  highlightWord?: string;
  highlightClassName?: string;
}

export function LetterReveal({
  text,
  className = "",
  delay = 500,
  letterDelay = 50,
  highlightWord,
  highlightClassName = "",
}: LetterRevealProps) {
  const [visibleLetters, setVisibleLetters] = useState(0);
  const [started, setStarted] = useState(false);

  useEffect(() => {
    const startTimer = setTimeout(() => {
      setStarted(true);
    }, delay);

    return () => clearTimeout(startTimer);
  }, [delay]);

  useEffect(() => {
    if (!started) return;

    if (visibleLetters < text.length) {
      const timer = setTimeout(() => {
        setVisibleLetters((prev) => prev + 1);
      }, letterDelay);

      return () => clearTimeout(timer);
    }
  }, [visibleLetters, text.length, letterDelay, started]);

  // Helper function to determine if a letter is part of the highlight word
  const getLetterClassName = (index: number) => {
    if (!highlightWord || !highlightClassName) {
      return className;
    }

    const words = text.split(" ");
    let currentIndex = 0;

    for (const word of words) {
      if (
        word === highlightWord &&
        index >= currentIndex &&
        index < currentIndex + word.length
      ) {
        return highlightClassName;
      }
      currentIndex += word.length + 1; // +1 for space
    }

    return className;
  };

  return (
    <span>
      {text.split("").map((letter, index) => (
        <span
          key={index}
          className={`inline-block transition-all duration-300 ${getLetterClassName(index)} ${
            index < visibleLetters
              ? "opacity-100 translate-y-0"
              : "opacity-0 translate-y-4"
          }`}
          style={{
            transitionDelay: `${index * letterDelay}ms`,
          }}
        >
          {letter === " " ? "\u00A0" : letter}
        </span>
      ))}
    </span>
  );
}
