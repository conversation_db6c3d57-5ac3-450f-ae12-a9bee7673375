import { useEffect, useState } from 'react';

interface LetterRevealProps {
  text: string;
  className?: string;
  delay?: number;
  letterDelay?: number;
}

export function LetterReveal({ 
  text, 
  className = '', 
  delay = 500, 
  letterDelay = 50 
}: LetterRevealProps) {
  const [visibleLetters, setVisibleLetters] = useState(0);
  const [started, setStarted] = useState(false);

  useEffect(() => {
    const startTimer = setTimeout(() => {
      setStarted(true);
    }, delay);

    return () => clearTimeout(startTimer);
  }, [delay]);

  useEffect(() => {
    if (!started) return;

    if (visibleLetters < text.length) {
      const timer = setTimeout(() => {
        setVisibleLetters(prev => prev + 1);
      }, letterDelay);

      return () => clearTimeout(timer);
    }
  }, [visibleLetters, text.length, letterDelay, started]);

  return (
    <span className={className}>
      {text.split('').map((letter, index) => (
        <span
          key={index}
          className={`inline-block transition-all duration-300 ${
            index < visibleLetters
              ? 'opacity-100 translate-y-0'
              : 'opacity-0 translate-y-4'
          }`}
          style={{
            transitionDelay: `${index * letterDelay}ms`
          }}
        >
          {letter === ' ' ? '\u00A0' : letter}
        </span>
      ))}
    </span>
  );
}
