import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";
import { User, LogOut } from "lucide-react";
import { useEffect, useState } from "react";

interface AuthButtonProps {
  variant?: "default" | "outline" | "ghost";
  className?: string;
}

export function AuthButton({
  variant = "outline",
  className = "",
}: AuthButtonProps) {
  const [authError, setAuthError] = useState<string | null>(null);

  // Try to get auth state, but provide fallback if it fails
  let user, logout, loading, isAuthenticated;

  try {
    const authState = useAuth();
    user = authState.user;
    logout = authState.logout;
    loading = authState.loading;
    isAuthenticated = authState.isAuthenticated;
  } catch (error) {
    console.error("Auth hook error:", error);
    setAuthError("Authentication unavailable");
    // Fallback values
    user = null;
    logout = async () => {};
    loading = false;
    isAuthenticated = false;
  }

  const handleLogout = async () => {
    try {
      await logout();
      // Redirect to home page after logout
      window.location.href = "/";
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  // Show error state if auth is unavailable
  if (authError) {
    return (
      <a href="/login">
        <Button variant={variant} className={className}>
          Login
        </Button>
      </a>
    );
  }

  if (loading) {
    return (
      <Button
        variant={variant}
        className={`${className} opacity-50 cursor-not-allowed`}
        disabled
      >
        Loading...
      </Button>
    );
  }

  if (isAuthenticated && user) {
    return (
      <div className="flex items-center space-x-2">
        {/* User Info */}
        <div className="hidden md:flex items-center space-x-2 text-sm">
          <User className="h-4 w-4" />
          <span className="text-gray-600">
            {user.user_metadata?.firstName ||
              user.email?.split("@")[0] ||
              "User"}
          </span>
        </div>

        {/* Logout Button */}
        <Button
          variant={variant}
          onClick={handleLogout}
          className={`${className} flex items-center space-x-1`}
        >
          <LogOut className="h-4 w-4" />
          <span>Logout</span>
        </Button>
      </div>
    );
  }

  // Not authenticated - show login button
  return (
    <a href="/login">
      <Button variant={variant} className={className}>
        Login
      </Button>
    </a>
  );
}
